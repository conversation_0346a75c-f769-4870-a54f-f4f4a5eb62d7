/* pages/reviewDetail/reviewDetail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面标题 */
.header {
  background-color: #fff;
  padding: 40rpx 0;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 订单信息 */
.order-info {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-content {
  display: flex;
  align-items: center;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.service-price {
  font-size: 28rpx;
  color: #ff4f8f;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

/* 评价内容区域 */
.review-content {
  margin: 20rpx;
}

/* 评分区域 */
.rating-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.rating-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stars {
  display: flex;
}

.star {
  margin-right: 8rpx;
}

.star-icon {
  font-size: 48rpx;
  color: #ddd;
}

.star.active .star-icon {
  color: #ffd700;
  text-shadow: 0 0 8rpx rgba(255, 215, 0, 0.3);
}

.rating-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.rating-score {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4f8f;
}

/* 评价文字区域 */
.comment-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.comment-content {
  background-color: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #ff4f8f;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 评价图片区域 */
.photo-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}

.photo-item {
  width: 200rpx;
  height: 200rpx;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

/* 评价时间 */
.review-time {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}

.time-text {
  font-size: 26rpx;
  color: #999;
}

/* 无评价提示 */
.no-review {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.no-review-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.no-review-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.no-review-desc {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff4f8f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
