// pages/reviewDetail/reviewDetail.js
import orderApi from '../../api/modules/order';
import reviewApi from '../../api/modules/review';
import utils from '../utils/util';

Page({
  data: {
    orderId: '',
    orderDetail: null,
    reviewData: null,
    loading: true,
    ratingTexts: ['很差', '较差', '一般', '满意', '非常满意'],
    userInfo: null,
  },

  onLoad(options) {
    const { orderId } = options;
    if (orderId) {
      this.setData({ orderId });
      this.loadData();
    }
    
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    }
  },

  /**
   * 加载数据
   */
  async loadData() {
    try {
      wx.showLoading({ title: '加载中...' });
      const { userInfo, orderId } = this.data;
      
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 并行加载订单详情和评价数据
      const [orderDetail, reviewData] = await Promise.all([
        orderApi.getDetail(userInfo.id, orderId),
        this.loadReviewData(orderId)
      ]);

      // 格式化订单时间
      if (orderDetail && orderDetail.createdAt) {
        orderDetail.createdAt = utils.formatNormalDate(orderDetail.createdAt);
      }

      // 格式化评价时间
      if (reviewData && reviewData.createdAt) {
        reviewData.createdAt = utils.formatNormalDate(reviewData.createdAt);
      }

      this.setData({ 
        orderDetail,
        reviewData,
        loading: false
      });
      
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载评价数据
   */
  async loadReviewData(orderId) {
    try {
      const reviewData = await reviewApi.getByOrderId(orderId);
      return reviewData;
    } catch (error) {
      // 如果是404错误，说明没有评价，返回null
      if (error.statusCode === 404) {
        return null;
      }
      throw error;
    }
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const { reviewData } = this.data;
    
    wx.previewImage({
      current: url,
      urls: reviewData.photoURLs || []
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '查看我的服务评价',
      path: `/pages/reviewDetail/reviewDetail?orderId=${this.data.orderId}`,
      imageUrl: this.data.reviewData?.photoURLs?.[0] || ''
    };
  }
});
